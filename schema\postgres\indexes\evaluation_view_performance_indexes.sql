-- Performance indexes for evaluation views
-- These indexes improve performance for vwEvaluationOverview, vwEvaluationQuestionData, and vwEvaluationGroupData

-- 1. Index for evaldata.status filtering (used heavily in vwEvaluationOverview)
CREATE INDEX IF NOT EXISTS evaldata_status_idx ON evaldata USING btree (status ASC NULLS LAST);

-- 2. Index for evaldata.userid joins (used in all evaluation views)
CREATE INDEX IF NOT EXISTS evaldata_userid_idx ON evaldata USING btree (userid ASC NULLS LAST);

-- 3. Index for evaldata.evaluatorid joins (used in vwEvaluationOverview)
CREATE INDEX IF NOT EXISTS evaldata_evaluatorid_idx ON evaldata USING btree (evaluatorid ASC NULLS LAST);

-- 4. Composite index for evaldetails joins (used in vwEvaluationQuestionData)
CREATE INDEX IF NOT EXISTS evaldetails_composite_idx ON evaldetails USING btree (
    evaluationformid ASC NULLS LAST,
    questiongroupid ASC NULLS LAST,
    questionid ASC NULLS LAST,
    questionanwserid ASC NULLS LAST
);

-- 5. Index for evaldetails.questiongroupid (used in vwEvaluationGroupData subquery)
CREATE INDEX IF NOT EXISTS evaldetails_questiongroupid_idx ON evaldetails USING btree (questiongroupid ASC NULLS LAST);

-- 6. Index for userdetails.manager (used in vwUserDetail which is used by evaluation views)
CREATE INDEX IF NOT EXISTS userdetails_manager_idx ON userdetails USING btree (manager ASC NULLS LAST);

-- 7. Index for userdetails.divisionid (used in vwUserDetail)
CREATE INDEX IF NOT EXISTS userdetails_divisionid_idx ON userdetails USING btree (divisionid ASC NULLS LAST);

-- 8. Composite index for evaldata filtering and joins (optimizes the main query in vwEvaluationOverview)
CREATE INDEX IF NOT EXISTS evaldata_status_userid_evaluatorid_idx ON evaldata USING btree (
    status ASC NULLS LAST,
    userid ASC NULLS LAST,
    evaluatorid ASC NULLS LAST
);

-- 9. Index for evaldata date filtering (useful for date-based queries on views)
CREATE INDEX IF NOT EXISTS evaldata_releasedate_idx ON evaldata USING btree (releasedate ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS evaldata_assigneddate_idx ON evaldata USING btree (assigneddate ASC NULLS LAST);

-- Comments explaining the purpose of each index
COMMENT ON INDEX evaldata_status_idx IS 'Improves performance for status filtering in evaluation views';
COMMENT ON INDEX evaldata_userid_idx IS 'Optimizes user joins in evaluation views';
COMMENT ON INDEX evaldata_evaluatorid_idx IS 'Optimizes evaluator joins in evaluation overview';
COMMENT ON INDEX evaldetails_composite_idx IS 'Optimizes complex joins in evaluation question data view';
COMMENT ON INDEX evaldetails_questiongroupid_idx IS 'Optimizes subquery in evaluation group data view';
COMMENT ON INDEX userdetails_manager_idx IS 'Improves manager hierarchy lookups in user detail view';
COMMENT ON INDEX userdetails_divisionid_idx IS 'Optimizes division joins in user detail view';
COMMENT ON INDEX evaldata_status_userid_evaluatorid_idx IS 'Composite index for main evaluation overview query optimization';
COMMENT ON INDEX evaldata_releasedate_idx IS 'Optimizes date-based filtering on evaluation views';
COMMENT ON INDEX evaldata_assigneddate_idx IS 'Optimizes date-based filtering on evaluation views';
