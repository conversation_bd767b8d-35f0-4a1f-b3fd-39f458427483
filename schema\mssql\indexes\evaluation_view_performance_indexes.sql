-- Performance indexes for evaluation views
-- These indexes improve performance for vwEvaluationOverview, vwEvaluationQuestionData, and vwEvaluationGroupData

-- 1. Index for evalData.status filtering (used heavily in vwEvaluationOverview)
IF dbo.csg_index_exists('evaldata_status_idx', 'evalData') = 0
CREATE INDEX [evaldata_status_idx] ON [evalData]([status]);

-- 2. Index for evalData.userid joins (used in all evaluation views)
IF dbo.csg_index_exists('evaldata_userid_idx', 'evalData') = 0
CREATE INDEX [evaldata_userid_idx] ON [evalData]([userid]);

-- 3. Index for evalData.evaluatorid joins (used in vwEvaluationOverview)
IF dbo.csg_index_exists('evaldata_evaluatorid_idx', 'evalData') = 0
CREATE INDEX [evaldata_evaluatorid_idx] ON [evalData]([evaluatorid]);

-- 4. Composite index for evalDetails joins (used in vwEvaluationQuestionData)
IF dbo.csg_index_exists('evaldetails_composite_idx', 'evalDetails') = 0
CREATE INDEX [evaldetails_composite_idx] ON [evalDetails]([evaluationformid], [questiongroupid], [questionid], [questionanwserid]);

-- 5. Index for evalDetails.questiongroupid (used in vwEvaluationGroupData subquery)
IF dbo.csg_index_exists('evaldetails_questiongroupid_idx', 'evalDetails') = 0
CREATE INDEX [evaldetails_questiongroupid_idx] ON [evalDetails]([questiongroupid]);

-- 6. Index for userDetails.manager (used in vwUserDetail which is used by evaluation views)
IF dbo.csg_index_exists('userdetails_manager_idx', 'userDetails') = 0
CREATE INDEX [userdetails_manager_idx] ON [userDetails]([manager]);

-- 7. Index for userDetails.divisionid (used in vwUserDetail)
IF dbo.csg_index_exists('userdetails_divisionid_idx', 'userDetails') = 0
CREATE INDEX [userdetails_divisionid_idx] ON [userDetails]([divisionid]);

-- 8. Composite index for evalData filtering and joins (optimizes the main query in vwEvaluationOverview)
IF dbo.csg_index_exists('evaldata_status_userid_evaluatorid_idx', 'evalData') = 0
CREATE INDEX [evaldata_status_userid_evaluatorid_idx] ON [evalData]([status], [userid], [evaluatorid]);

-- 9. Index for evalData date filtering (useful for date-based queries on views)
IF dbo.csg_index_exists('evaldata_releasedate_idx', 'evalData') = 0
CREATE INDEX [evaldata_releasedate_idx] ON [evalData]([releasedate]);

IF dbo.csg_index_exists('evaldata_assigneddate_idx', 'evalData') = 0
CREATE INDEX [evaldata_assigneddate_idx] ON [evalData]([assigneddate]);
