CREATE TABLE IF NOT EXISTS convvoicesentimentdetaildata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    starttime timestamp without time zone,
    starttimeltc timestamp without time zone,
    duration numeric(20, 2),
    participant varchar(50),
    phrase varchar(400),
    sentiment numeric(20, 2),
    phraseindex integer,
    updated timestamp without time zone,
    transcriptnumber varchar(50),
	communicationid varchar(50),
	ani varchar(200),
	dnis varchar(200),
	queueid varchar(50),
	userid varchar(50),
    CONSTRAINT convvoicesentimentdetaildata_pkey PRIMARY KEY (keyid)
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS convvoicesentimentdetaildata_conversationid_idx ON convvoicesentimentdetaildata USING btree (
    conversationid ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS convvoicesentimentdetaildata_updated_idx ON convvoicesentimentdetaildata USING btree (
    updated ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS convvoicesentimentdetaildata_sentiment_idx ON convvoicesentimentdetaildata USING btree (
    sentiment ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS convvoicesentimentdetaildata_participant_idx ON convvoicesentimentdetaildata USING btree (
    participant ASC NULLS LAST
) TABLESPACE pg_default;