IF dbo.csg_table_exists('convVoiceSentimentDetailData') = 0
CREATE TABLE [convVoiceSentimentDetailData](
    [keyid] [nvarchar](100) NOT NULL,
    [conversationid] [nvarchar](50),
    [starttime] [datetime],
    [starttimeLTC] [datetime],
    [duration] [decimal](20, 2),
    [participant] [nvarchar](50),
    [phrase] [nvarchar](400),
    [sentiment] [decimal](20, 2),
    [phraseindex] [int],
    [updated] [datetime],
    [transcriptnumber] [nvarchar](50),
	[communicationid] [nvarchar](50),
	[ani] [nvarchar](200),
	[dnis] [nvarchar](200),
	[queueid] [nvarchar](50),
	[userid] [nvarchar](50),
    CONSTRAINT [PK_convVoiceSentimentData] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('convVoiceSentimentDetailData', 'transcriptnumber') = 0
    ALTER TABLE convVoiceSentimentDetailData ADD transcriptnumber nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceSentimentDetailData ALTER COLUMN transcriptnumber nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceSentimentDetailData', 'communicationid') = 0
    ALTER TABLE dbo.convVoiceSentimentDetailData ADD communicationid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceSentimentDetailData ALTER COLUMN communicationid nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceSentimentDetailData', 'ani') = 0
    ALTER TABLE dbo.convVoiceSentimentDetailData ADD ani nvarchar(200) NULL;
ELSE
    ALTER TABLE convVoiceSentimentDetailData ALTER COLUMN ani nvarchar(200) NULL;

IF dbo.csg_column_exists('convVoiceSentimentDetailData', 'dnis') = 0
    ALTER TABLE dbo.convVoiceSentimentDetailData ADD dnis nvarchar(200) NULL;
ELSE
    ALTER TABLE convVoiceSentimentDetailData ALTER COLUMN dnis nvarchar(200) NULL;

IF dbo.csg_column_exists('convVoiceSentimentDetailData', 'queueid') = 0
    ALTER TABLE dbo.convVoiceSentimentDetailData ADD queueid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceSentimentDetailData ALTER COLUMN queueid nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceSentimentDetailData', 'userid') = 0
    ALTER TABLE dbo.convVoiceSentimentDetailData ADD userid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceSentimentDetailData ALTER COLUMN userid nvarchar(50) NULL;

-- Performance indexes for voice analytics views
IF dbo.csg_index_exists('convvoicesentimentdetaildata_conversationid_idx', 'convVoiceSentimentDetailData') = 0
CREATE INDEX [convvoicesentimentdetaildata_conversationid_idx] ON [convVoiceSentimentDetailData]([conversationid]);

IF dbo.csg_index_exists('convvoicesentimentdetaildata_updated_idx', 'convVoiceSentimentDetailData') = 0
CREATE INDEX [convvoicesentimentdetaildata_updated_idx] ON [convVoiceSentimentDetailData]([updated]);

IF dbo.csg_index_exists('convvoicesentimentdetaildata_sentiment_idx', 'convVoiceSentimentDetailData') = 0
CREATE INDEX [convvoicesentimentdetaildata_sentiment_idx] ON [convVoiceSentimentDetailData]([sentiment]);

IF dbo.csg_index_exists('convvoicesentimentdetaildata_participant_idx', 'convVoiceSentimentDetailData') = 0
CREATE INDEX [convvoicesentimentdetaildata_participant_idx] ON [convVoiceSentimentDetailData]([participant]);