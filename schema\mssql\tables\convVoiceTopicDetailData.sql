IF dbo.csg_table_exists('convVoiceTopicDetailData') = 0
CREATE TABLE [convVoiceTopicDetailData](
    [keyid] [nvarchar](100) NOT NULL,
    [conversationid] [nvarchar](50),
    [starttime] [datetime],
    [starttimeLTC] [datetime],
    [participant] [nvarchar](50),
    [duration] [decimal](20, 2),
    [confidence] [decimal](20, 2),
    [topicname] [nvarchar](200),
    [topicid] [nvarchar](50),
    [topicphrase] [nvarchar](200),
    [transcriptphrase] [nvarchar](200),
    [updated] [datetime],
    [transcriptnumber] [nvarchar](50),
	[communicationid] [nvarchar](50),
	[ani] [nvarchar](200),
	[dnis] [nvarchar](200),
	[queueid] [nvarchar](50),
	[userid] [nvarchar](50),
    CONSTRAINT [PK_convVoiceTopicDetailData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('convvoice_topic_conv', 'convVoiceTopicDetailData') = 0
    CREATE INDEX convvoice_topic_conv ON convVoiceTopicDetailData (conversationid);

IF dbo.csg_index_exists('convvoice_topic_participant', 'convVoiceTopicDetailData') = 0
    CREATE INDEX convvoice_topic_participant ON convVoiceTopicDetailData (participant);

IF dbo.csg_index_exists('convvoice_topic_start', 'convVoiceTopicDetailData') = 0
    CREATE INDEX convvoice_topic_start ON convVoiceTopicDetailData (starttime);

IF dbo.csg_index_exists('convvoice_topic_startltc', 'convVoiceTopicDetailData') = 0
    CREATE INDEX convvoice_topic_startltc ON convVoiceTopicDetailData (starttimeLTC);

IF dbo.csg_index_exists('convvoice_topic_topicid', 'convVoiceTopicDetailData') = 0
    CREATE INDEX convvoice_topic_topicid ON convVoiceTopicDetailData (topicid);

IF dbo.csg_index_exists('convvoice_topic_topicphrase', 'convVoiceTopicDetailData') = 0
    CREATE INDEX convvoice_topic_topicphrase ON convVoiceTopicDetailData (topicphrase);

IF dbo.csg_column_exists('convVoiceTopicDetailData', 'transcriptnumber') = 0
    ALTER TABLE convVoiceTopicDetailData ADD transcriptnumber nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceTopicDetailData ALTER COLUMN transcriptnumber nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceTopicDetailData', 'communicationid') = 0
    ALTER TABLE dbo.convVoiceTopicDetailData ADD communicationid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceTopicDetailData ALTER COLUMN communicationid nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceTopicDetailData', 'ani') = 0
    ALTER TABLE dbo.convVoiceTopicDetailData ADD ani nvarchar(200) NULL;
ELSE
    ALTER TABLE convVoiceTopicDetailData ALTER COLUMN ani nvarchar(200) NULL;

IF dbo.csg_column_exists('convVoiceTopicDetailData', 'dnis') = 0
    ALTER TABLE dbo.convVoiceTopicDetailData ADD dnis nvarchar(200) NULL;
ELSE
    ALTER TABLE convVoiceTopicDetailData ALTER COLUMN dnis nvarchar(200) NULL;

IF dbo.csg_column_exists('convVoiceTopicDetailData', 'queueid') = 0
    ALTER TABLE dbo.convVoiceTopicDetailData ADD queueid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceTopicDetailData ALTER COLUMN queueid nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceTopicDetailData', 'userid') = 0
    ALTER TABLE dbo.convVoiceTopicDetailData ADD userid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceTopicDetailData ALTER COLUMN userid nvarchar(50) NULL;

-- Performance indexes for voice analytics views
IF dbo.csg_index_exists('convvoicetopicdetaildata_conversationid_idx', 'convVoiceTopicDetailData') = 0
CREATE INDEX [convvoicetopicdetaildata_conversationid_idx] ON [convVoiceTopicDetailData]([conversationid]);

IF dbo.csg_index_exists('convvoicetopicdetaildata_updated_idx', 'convVoiceTopicDetailData') = 0
CREATE INDEX [convvoicetopicdetaildata_updated_idx] ON [convVoiceTopicDetailData]([updated]);

IF dbo.csg_index_exists('convvoicetopicdetaildata_confidence_idx', 'convVoiceTopicDetailData') = 0
CREATE INDEX [convvoicetopicdetaildata_confidence_idx] ON [convVoiceTopicDetailData]([confidence]);

IF dbo.csg_index_exists('convvoicetopicdetaildata_topicid_idx', 'convVoiceTopicDetailData') = 0
CREATE INDEX [convvoicetopicdetaildata_topicid_idx] ON [convVoiceTopicDetailData]([topicid]);