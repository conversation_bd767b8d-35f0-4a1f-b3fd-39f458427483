-- Cleanup script to remove all MVW-related cron jobs
-- This script should be run once during the MVW to Views migration
-- to ensure all old materialized view maintenance jobs are removed

SET search_path TO cron;

-- Function to safely unschedule jobs if they exist
DO $$
DECLARE
    job_record RECORD;
    jobs_to_remove TEXT[] := ARRAY[
        'mvwconvvoiceoverviewdata',
        'mvwconvvoicetopicdetaildata', 
        'mvwconvvoicesentimentdetaildata',
        'mvwevaluationoverview',
        'mvwevaluationgroupdata',
        'evaluationquestiondata',
        'mvwconvvoiceoverviewdata_customername',
        'mvwconvvoicetopicdetaildata_customername',
        'mvwconvvoicesentimentdetaildata_customername', 
        'mvwevaluationoverview_customername',
        'mvwevaluationgroupdata_customername',
        'evaluationquestiondata_customername'
    ];
    job_name TEXT;
    removed_count INTEGER := 0;
BEGIN
    RAISE NOTICE 'Starting cleanup of MVW cron jobs...';
    
    -- Loop through each job name to remove
    FOREACH job_name IN ARRAY jobs_to_remove
    LOOP
        -- Check if job exists and remove it
        IF EXISTS (SELECT 1 FROM cron.job WHERE jobname = job_name) THEN
            PERFORM cron.unschedule(job_name);
            removed_count := removed_count + 1;
            RAISE NOTICE 'Removed cron job: %', job_name;
        ELSE
            RAISE NOTICE 'Cron job not found (already removed): %', job_name;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'MVW cron job cleanup completed. Removed % jobs.', removed_count;
    
    -- Show remaining jobs for verification
    RAISE NOTICE 'Remaining cron jobs:';
    FOR job_record IN 
        SELECT jobname, schedule, command 
        FROM cron.job 
        ORDER BY jobname
    LOOP
        RAISE NOTICE 'Job: % | Schedule: % | Command: %', 
            job_record.jobname, 
            job_record.schedule, 
            LEFT(job_record.command, 50) || CASE WHEN LENGTH(job_record.command) > 50 THEN '...' ELSE '' END;
    END LOOP;
    
END $$;
