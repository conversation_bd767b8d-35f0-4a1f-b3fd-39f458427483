
CREATE TABLE IF NOT EXISTS evaldata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50) NOT NULL,
    evaluationid varchar(50) NOT NULL,
    calibrationid varchar(50),
    evaluationformid varchar(50),
    evaluatorid varchar(50),
    userid varchar(50),
    status varchar(20),
    totalscore numeric(20, 2),
    averagescore numeric(20, 2),
    lowscore numeric(20, 2),
    highscore numeric(20, 2),
    totalcriticalscore numeric(20, 2),
    totalnoncriticalscore numeric(20, 2),
    agenthasread bit(1),
    releasedate timestamp without time zone,
    releasedateltc timestamp without time zone,
    assigneddate timestamp without time zone,
    assigneddateltc timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT evaldata_pkey PRIMARY KEY (keyid)
);

CREATE INDEX IF NOT EXISTS evaldataconversationid ON evaldata USING btree (
    conversationid ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS evaldataevaluationid ON evaldata USING btree (
    evaluationid ASC NULLS LAST
) TABLESPACE pg_default;

-- Additional indexes for evaluation view performance using csg_index_exists
DO $$
BEGIN
    IF NOT csg_index_exists('evaldata_status_idx', 'evaldata') THEN
        CREATE INDEX evaldata_status_idx ON evaldata USING btree (status ASC NULLS LAST);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT csg_index_exists('evaldata_userid_idx', 'evaldata') THEN
        CREATE INDEX evaldata_userid_idx ON evaldata USING btree (userid ASC NULLS LAST);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT csg_index_exists('evaldata_evaluatorid_idx', 'evaldata') THEN
        CREATE INDEX evaldata_evaluatorid_idx ON evaldata USING btree (evaluatorid ASC NULLS LAST);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT csg_index_exists('evaldata_releasedate_idx', 'evaldata') THEN
        CREATE INDEX evaldata_releasedate_idx ON evaldata USING btree (releasedate ASC NULLS LAST);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT csg_index_exists('evaldata_assigneddate_idx', 'evaldata') THEN
        CREATE INDEX evaldata_assigneddate_idx ON evaldata USING btree (assigneddate ASC NULLS LAST);
    END IF;
END $$;

-- Composite index for evaluation overview query optimization
DO $$
BEGIN
    IF NOT csg_index_exists('evaldata_status_userid_evaluatorid_idx', 'evaldata') THEN
        CREATE INDEX evaldata_status_userid_evaluatorid_idx ON evaldata USING btree (
            status ASC NULLS LAST,
            userid ASC NULLS LAST,
            evaluatorid ASC NULLS LAST
        );
    END IF;
END $$;

ALTER TABLE evaldata ADD IF NOT EXISTS calibrationid varchar(50) NULL;
ALTER TABLE evaldata ADD IF NOT EXISTS averagescore numeric(20, 2) NULL;
ALTER TABLE evaldata ADD IF NOT EXISTS lowscore numeric(20, 2) NULL;
ALTER TABLE evaldata ADD IF NOT EXISTS highscore numeric(20, 2) NULL;
ALTER TABLE evaldata ALTER COLUMN calibrationid DROP NOT NULL;
