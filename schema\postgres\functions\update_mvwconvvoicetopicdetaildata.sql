DROP PROCEDURE IF EXISTS update_mvwconvvoicetopicdetaildata(date);

DROP PROCEDURE IF EXISTS update_mvwconvvoicetopicdetaildata();

CREATE OR REPLACE PROCEDURE update_mvwconvvoicetopicdetaildata()
LANGUAGE plpgsql
AS $$
DECLARE
    record_count bigint;
    effective_from_date date;
BEGIN
    -- Check if the mvwconvvoicetopicdetaildata table is empty
    SELECT COUNT(*) INTO record_count FROM mvwconvvoicetopicdetaildata;

    -- Determine the effective from_date dynamically
    IF record_count = 0 THEN
        -- If the table is empty, update from '2000-01-01'
        effective_from_date := '2000-01-01';
    ELSE
        -- If the table is not empty, update from 30 days ago
        effective_from_date := now() - interval '30 days';
    END IF;

    -- Insert or update data based on the determined effective_from_date
    INSERT INTO mvwconvvoicetopicdetaildata
    (
        SELECT
            ct.keyid,
            ct.conversationid,
            ct.starttime,
            ct.starttimeltc,
            ct.participant,
            ct.duration,
            ct.confidence,
            ct.topicname,
            ct.topicid,
            ct.topicphrase,
            ct.transcriptphrase,
            ct.updated,
            cs.conversationstartdate,
            cs.conversationstartdateltc,
            cs.conversationenddate,
            cs.conversationenddateltc,
            cs.ttalkcomplete,
            cs.ani,
            cs.dnis,
            cs.firstmediatype,
            cs.divisionid,
            cs.firstqueueid,
            qd1.name AS firstqueuename,
            cs.lastqueueid,
            qd2.name AS lastqueuename,
            cs.firstagentid,
            ud1.name AS firstagentname,
            ud1.department AS firstagentdept,
            ud1.managerid AS firstagentmanagerid,
            ud1.managername AS firstagentmanagername,
            cs.lastagentid,
            ud2.name AS lastagentname,
            ud2.department AS lastagentdept,
            ud2.managerid AS lastagentmanagerid,
            ud2.managername AS lastagentmanagername,
            cs.firstwrapupcode,
            cs.firstwrapupname,
            cs.lastwrapupcode,
            cs.lastwrapupname,
            dd.name AS divisionname
        FROM convvoicetopicdetaildata ct
        LEFT JOIN convsummarydata cs ON cs.conversationid::text = ct.conversationid::text
        LEFT JOIN queuedetails qd1 ON qd1.id::text = cs.firstqueueid::text
        LEFT JOIN queuedetails qd2 ON qd2.id::text = cs.lastqueueid::text
        LEFT JOIN userdetails ud1 ON ud1.id::text = cs.firstagentid::text
        LEFT JOIN userdetails ud2 ON ud2.id::text = cs.lastagentid::text
        LEFT JOIN divisiondetails dd ON dd.id::text = cs.divisionid::text
        WHERE cs.conversationstartdate >= effective_from_date OR ct.updated >= effective_from_date
    )
    ON CONFLICT ON CONSTRAINT mvwconvvoicetopicdetaildata_pkey
    DO UPDATE SET
        keyid = EXCLUDED.keyid,
        conversationid = EXCLUDED.conversationid,
        starttime = EXCLUDED.starttime,
        starttimeltc = EXCLUDED.starttimeltc,
        participant = EXCLUDED.participant,
        duration = EXCLUDED.duration,
        confidence = EXCLUDED.confidence,
        topicname = EXCLUDED.topicname,
        topicid = EXCLUDED.topicid,
        topicphrase = EXCLUDED.topicphrase,
        transcriptphrase = EXCLUDED.transcriptphrase,
        updated = EXCLUDED.updated,
        conversationstartdate = EXCLUDED.conversationstartdate,
        conversationstartdateltc = EXCLUDED.conversationstartdateltc,
        conversationenddate = EXCLUDED.conversationenddate,
        conversationenddateltc = EXCLUDED.conversationenddateltc,
        ttalkcomplete = EXCLUDED.ttalkcomplete,
        ani = EXCLUDED.ani,
        dnis = EXCLUDED.dnis,
        firstmediatype = EXCLUDED.firstmediatype,
        divisionid = EXCLUDED.divisionid,
        firstqueueid = EXCLUDED.firstqueueid,
        firstqueuename = EXCLUDED.firstqueuename,
        lastqueueid = EXCLUDED.lastqueueid,
        lastqueuename = EXCLUDED.lastqueuename,
        firstagentid = EXCLUDED.firstagentid,
        firstagentname = EXCLUDED.firstagentname,
        firstagentdept = EXCLUDED.firstagentdept,
        firstagentmanagerid = EXCLUDED.firstagentmanagerid,
        firstagentmanagername = EXCLUDED.firstagentmanagername,
        lastagentid = EXCLUDED.lastagentid,
        lastagentname = EXCLUDED.lastagentname,
        lastagentdept = EXCLUDED.lastagentdept,
        lastagentmanagerid = EXCLUDED.lastagentmanagerid,
        lastagentmanagername = EXCLUDED.lastagentmanagername,
        firstwrapupcode = EXCLUDED.firstwrapupcode,
        firstwrapupname = EXCLUDED.firstwrapupname,
        lastwrapupcode = EXCLUDED.lastwrapupcode,
        lastwrapupname = EXCLUDED.lastwrapupname,
        divisionname = EXCLUDED.divisionname;
END;
$$;

-- Example call
CALL update_mvwconvvoicetopicdetaildata();