-- Drop the materialized view if it exists
DROP MATERIALIZED VIEW IF EXISTS mvwevaluationquestiondata;

-- Create regular view to replace the materialized view
CREATE OR REPLACE VIEW vwEvaluationQuestionData AS
SELECT
  DISTINCT eq.keyid,
  eq.evaluationid,
  eq.questionid,
  eq.answerid,
  eq.score,
  ed.questionanswervalue,
  eq.markedna,
  eq.failedkillquestions,
  eq.comments,
  ed.evaluationformid,
  ed.evaluationname,
  ed.questiongroupid,
  ed.questiongroupname,
  ed.questiontext,
  ed.questionhelptext,
  ed.quesiontype,
  ed.questionanwserid,
  ed.questionanswertext,
  cd.divisionid
FROM
  evalquestiondata eq
  LEFT JOIN evaldetails ed ON ed.evaluationformid :: text = eq.evaluationformid :: text
  AND ed.questiongroupid :: text = eq.questiongroupid :: text
  AND ed.questionid :: text = eq.questionid :: text
  AND ed.questionanwserid :: text = eq.answerid :: text
  LEFT JOIN evaldata eda ON eda.evaluationid :: text = eq.evaluationid :: text
  LEFT JOIN convsummarydata cd ON cd.conversationid :: text = eda.conversationid :: text;

COMMENT ON COLUMN vwEvaluationQuestionData.keyid IS 'Primary Key';
COMMENT ON COLUMN vwEvaluationQuestionData.evaluationid IS 'Evaluation GUID';
COMMENT ON COLUMN vwEvaluationQuestionData.questionid IS 'Question GUID';
COMMENT ON COLUMN vwEvaluationQuestionData.answerid IS 'Answer GUID';
COMMENT ON COLUMN vwEvaluationQuestionData.score IS 'Score';
COMMENT ON COLUMN vwEvaluationQuestionData.questionanswervalue IS 'Question answer value';
COMMENT ON COLUMN vwEvaluationQuestionData.markedna IS 'Marked as NA';
COMMENT ON COLUMN vwEvaluationQuestionData.failedkillquestions IS 'Failed kill questions';
COMMENT ON COLUMN vwEvaluationQuestionData.comments IS 'Comments';
COMMENT ON COLUMN vwEvaluationQuestionData.evaluationformid IS 'Evaluation form GUID';
COMMENT ON COLUMN vwEvaluationQuestionData.evaluationname IS 'Evaluation name';
COMMENT ON COLUMN vwEvaluationQuestionData.questiongroupid IS 'Question group GUID';
COMMENT ON COLUMN vwEvaluationQuestionData.questiongroupname IS 'Question group name';
COMMENT ON COLUMN vwEvaluationQuestionData.questiontext IS 'Question text';
COMMENT ON COLUMN vwEvaluationQuestionData.questionhelptext IS 'Question help text';
COMMENT ON COLUMN vwEvaluationQuestionData.quesiontype IS 'Question type';
COMMENT ON COLUMN vwEvaluationQuestionData.questionanwserid IS 'Question answer GUID';
COMMENT ON COLUMN vwEvaluationQuestionData.questionanswertext IS 'Question answer text';
COMMENT ON COLUMN vwEvaluationQuestionData.divisionid IS 'Division GUID';

COMMENT ON VIEW vwEvaluationQuestionData IS 'View for evaluation question data (converted from materialized view)';

-- Backward compatibility alias for existing queries
CREATE OR REPLACE VIEW mvwevaluationquestiondata AS
SELECT * FROM vwEvaluationQuestionData;

COMMENT ON VIEW mvwevaluationquestiondata IS 'Backward compatibility alias for vwEvaluationQuestionData - DEPRECATED: Use vwEvaluationQuestionData instead';
