-- Drop the materialized view if it exists
DROP MATERIALIZED VIEW IF EXISTS mvwevaluationoverview;

-- Create regular view to replace the materialized view
CREATE OR REPLACE VIEW vwEvaluationOverview AS
SELECT
  concat(
    evaldata.conversationid,
    '|',
    evaldata.evaluationid,
    '|',
    evaldata.evaluationformid
  ) AS keyid,
  evaldata.conversationid,
  CONCAT ('Conversation ID: ', evaldata.conversationid) AS "Title Conv",
  evaldata.evaluationid,
  CONCAT ('Eval ID: ', evaluationid) AS "Title Eval",
  case when evaldata.calibrationid is null then FALSE else TRUE end AS iscalibration,
  evaldata.evaluationformid,
  evaldata.evaluatorid,
  CONCAT ('Evaluator: ', vwuserdetail.name) AS "Title Evaluator",
  evaldata.userid,
  evaldata.status,
  evaldata.totalscore,
  evaldata.totalcriticalscore,
  evaldata.totalnoncriticalscore,
  evaldata.agenthasread :: varchar :: boolean AS agenthasread,
  evaldata.releasedate,
  evaldata.releasedateltc,
  evaldata.releasedateltc + (
    (
      SELECT
        timezonecalcs.diff
      FROM
        timezonecalcs('Australia/Sydney' :: text) timezonecalcs(utctime, ltctime, diff, timezonechosen)
    )
  ) :: double precision * '00:00:01' :: interval AS releasedateusrtz,
  evaldata.assigneddate,
  evaldata.assigneddateltc,
  userdetails.name AS "Agent Name",
  userdetails.department AS "Agent Dept",
  userdetails.managerid AS "Agent Manager Id",
  userdetails.managername AS "Agent Manager Name",
  userdetails.divisionid AS "Agent Div",
  vwuserdetail.name AS "Evaluator Name",
  vwuserdetail.managerid AS "Evaluator Manager Id",
  vwuserdetail.managername AS "Evaluator Manager Name",
  convsumm.conversationstartdate AS convstartdate,
  convsumm.conversationenddate AS convenddate,
  CAST(
    SUM(evaldata.totalscore) / Count(evaldata.conversationid) AS INTEGER
  ) as average_score,
  case
    when evaldata.totalscore > 80 then 1
    else 0
  end as Over80,
  convsumm.firstqueuename AS queuename,
  convsumm.firstmediatype AS mediatype
FROM
  evaldata evaldata
  LEFT JOIN vwuserdetail userdetails ON evaldata.userid :: text = userdetails.id :: text
  LEFT JOIN vwuserdetail vwuserdetail ON evaldata.evaluatorid :: text = vwuserdetail.id :: text
  LEFT JOIN vwconvsummarydata convsumm ON convsumm.conversationid :: text = evaldata.conversationid :: text
WHERE
  evaldata.status::text = 'FINISHED'::text
group by
  convsumm.conversationenddate,
  convsumm.conversationstartdate,
  convsumm.firstmediatype,
  convsumm.firstqueuename,
  evaldata.agenthasread,
  evaldata.assigneddate,
  evaldata.assigneddateltc,
  evaldata.calibrationid,
  evaldata.conversationid,
  evaldata.evaluationformid,
  evaldata.evaluationid,
  evaldata.evaluatorid,
  evaldata.releasedate,
  evaldata.releasedate,
  evaldata.releasedateltc,
  evaldata.releasedateltc,
  evaldata.status,
  evaldata.totalcriticalscore,
  evaldata.totalnoncriticalscore,
  evaldata.totalscore,
  evaldata.userid,
  userdetails.department,
  userdetails.divisionid,
  userdetails.managerid,
  userdetails.managername,
  userdetails.name,
  vwuserdetail.managerid,
  vwuserdetail.managername,
  vwuserdetail.name;

-- Add comments
COMMENT ON VIEW vwEvaluationOverview IS 'View for evaluation overview data (converted from materialized view)';
COMMENT ON COLUMN vwEvaluationOverview.keyid IS 'Primary Key';
COMMENT ON COLUMN vwEvaluationOverview.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN vwEvaluationOverview."Title Conv" IS 'Title for Conversation';
COMMENT ON COLUMN vwEvaluationOverview.evaluationid IS 'Evaluation GUID';
COMMENT ON COLUMN vwEvaluationOverview."Title Eval" IS 'Title for Evaluation';
COMMENT ON COLUMN vwEvaluationOverview.iscalibration IS 'Evaluation is a Calibration';
COMMENT ON COLUMN vwEvaluationOverview.evaluationformid IS 'Evaluation Form GUID';
COMMENT ON COLUMN vwEvaluationOverview.evaluatorid IS 'Evaluator GUID';
COMMENT ON COLUMN vwEvaluationOverview."Title Evaluator" IS 'Title for Evaluator';
COMMENT ON COLUMN vwEvaluationOverview.userid IS 'User GUID';
COMMENT ON COLUMN vwEvaluationOverview.status IS 'Status';
COMMENT ON COLUMN vwEvaluationOverview.totalscore IS 'Total Score';
COMMENT ON COLUMN vwEvaluationOverview.totalcriticalscore IS 'Total Critical Score';
COMMENT ON COLUMN vwEvaluationOverview.totalnoncriticalscore IS 'Total Non-Critical Score';
COMMENT ON COLUMN vwEvaluationOverview.agenthasread IS 'Agent has read';
COMMENT ON COLUMN vwEvaluationOverview.releasedate IS 'Release Date(UTC)';
COMMENT ON COLUMN vwEvaluationOverview.releasedateltc IS 'Release Date in(LTC)';
COMMENT ON COLUMN vwEvaluationOverview.releasedateusrtz IS 'Release Date in User Timezone';
COMMENT ON COLUMN vwEvaluationOverview.assigneddate IS 'Assigned Date(UTC)';
COMMENT ON COLUMN vwEvaluationOverview.assigneddateltc IS 'Assigned Date(LTC)';
COMMENT ON COLUMN vwEvaluationOverview."Agent Name" IS 'Agent Name';
COMMENT ON COLUMN vwEvaluationOverview."Agent Dept" IS 'Agent Department';
COMMENT ON COLUMN vwEvaluationOverview."Agent Manager Id" IS 'Agent Manager GUID';
COMMENT ON COLUMN vwEvaluationOverview."Agent Manager Name" IS 'Agent Manager Name';
COMMENT ON COLUMN vwEvaluationOverview."Agent Div" IS 'Agent Division';
COMMENT ON COLUMN vwEvaluationOverview."Evaluator Name" IS 'Evaluator Name';
COMMENT ON COLUMN vwEvaluationOverview."Evaluator Manager Id" IS 'Evaluator Manager GUID';
COMMENT ON COLUMN vwEvaluationOverview."Evaluator Manager Name" IS 'Evaluator Manager Name';
COMMENT ON COLUMN vwEvaluationOverview.convstartdate IS 'Conversation Start Date';
COMMENT ON COLUMN vwEvaluationOverview.convenddate IS 'Conversation End Date';
COMMENT ON COLUMN vwEvaluationOverview.average_score IS 'Average Score';
COMMENT ON COLUMN vwEvaluationOverview.queuename IS 'Queue Name';
COMMENT ON COLUMN vwEvaluationOverview.mediatype IS 'Media Type';
