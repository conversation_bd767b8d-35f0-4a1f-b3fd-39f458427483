-- Drop the table if it exists (this will be replaced by a view)
IF OBJECT_ID('mvwevaluationgroupdata', 'U') IS NOT NULL
    DROP TABLE mvwevaluationgroupdata;
GO

-- Create view to replace the table (based on the logic from update_mvwevaluationgroupdata procedure)
CREATE VIEW vwEvaluationGroupData AS
SELECT
    egd.keyid,
    egd.evaluationid,
    egd.questiongroupid,
    (SELECT TOP 1 ed.questiongroupname
        FROM evaldetails ed
        WHERE ed.questiongroupid = egd.questiongroupid) AS questiongroupname,
    egd.totalscore,
    egd.maxtotalscore,
    egd.markedna,
    egd.totalcriticalscore,
    egd.maxtotalcriticalscore,
    egd.totalnoncriticalscore,
    egd.maxtotalnoncriticalscore,
    egd.totalscoreunweighted,
    egd.maxtotalscoreunweighted,
    egd.failedkillquestions,
    ud.divisionid,
    egd.comments,
    eda.conversationid
FROM evalQuestionGroupData egd
LEFT JOIN evalData eda ON eda.evaluationid = egd.evaluationid
LEFT JOIN userDetails ud ON ud.id = eda.userid;
GO

-- Backward compatibility alias for existing queries
CREATE OR ALTER VIEW mvwevaluationgroupdata AS
SELECT * FROM vwEvaluationGroupData;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'Backward compatibility alias for vwEvaluationGroupData - DEPRECATED: Use vwEvaluationGroupData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwevaluationgroupdata';
GO
