-- Backward compatibility aliases for converted mvw* views
-- These aliases ensure existing queries continue to work after the mvw* to vw* conversion

-- Alias for mvwevaluationoverview -> vwEvaluationOverview
CREATE OR ALTER VIEW mvwevaluationoverview AS
SELECT * FROM vwEvaluationOverview;
GO

-- Alias for mvwevaluationquestiondata -> vwEvaluationQuestionData  
CREATE OR ALTER VIEW mvwevaluationquestiondata AS
SELECT * FROM vwEvaluationQuestionData;
GO

-- Alias for mvwevaluationgroupdata -> vwEvaluationGroupData
CREATE OR ALTER VIEW mvwevaluationgroupdata AS
SELECT * FROM vwEvaluationGroupData;
GO

-- Add extended properties for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwEvaluationOverview - DEPRECATED: Use vwEvaluationOverview instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwevaluationoverview';
GO

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwEvaluationQuestionData - DEPRECATED: Use vwEvaluationQuestionData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwevaluationquestiondata';
GO

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwEvaluationGroupData - DEPRECATED: Use vwEvaluationGroupData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwevaluationgroupdata';
GO
