-- Drop the table if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoicetopicdetaildata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoicetopicdetaildata;
IF OBJECT_ID('mvwconvvoicetopicdetaildata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoicetopicdetaildata;
GO

-- Create regular view to replace the table
CREATE OR ALTER VIEW vwConvVoiceTopicDetailData AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.participant,
    ct.duration,
    ct.confidence,
    ct.topicname,
    ct.topicid,
    ct.topicphrase,
    ct.transcriptphrase,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    qd1.name AS firstqueuename,
    cs.lastqueueid,
    qd2.name AS lastqueuename,
    cs.firstagentid,
    ud1.name AS firstagentname,
    ud1.department AS firstagentdept,
    ud1.manager AS firstagentmanagerid,
    NULL AS firstagentmanagername,
    cs.lastagentid,
    ud2.name AS lastagentname,
    ud2.department AS lastagentdept,
    ud2.manager AS lastagentmanagerid,
    NULL AS lastagentmanagername,
    cs.firstwrapupcode,
    wd1.name AS firstwrapupname,
    cs.lastwrapupcode,
    wd2.name AS lastwrapupname,
    dd.name AS divisionname
FROM convVoiceTopicDetailData ct
LEFT JOIN convSummaryData cs ON cs.conversationid = ct.conversationid
LEFT JOIN queueDetails qd1 ON qd1.id = cs.firstqueueid
LEFT JOIN queueDetails qd2 ON qd2.id = cs.lastqueueid
LEFT JOIN wrapupDetails wd1 ON wd1.id = cs.firstwrapupcode
LEFT JOIN wrapupDetails wd2 ON wd2.id = cs.lastwrapupcode
LEFT JOIN userDetails ud1 ON ud1.id = cs.firstagentid
LEFT JOIN userDetails ud2 ON ud2.id = cs.lastagentid
LEFT JOIN divisionDetails dd ON dd.id = cs.divisionid;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'View for conversation voice topic detail data (converted from table)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'vwConvVoiceTopicDetailData';
GO
