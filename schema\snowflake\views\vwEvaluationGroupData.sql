-- Drop the table if it exists (this will be replaced by a view)
DROP TABLE IF EXISTS mvwevaluationgroupdata;

-- Create view to replace the table (based on the logic from update_mvwevaluationgroupdata procedure)
CREATE OR REPLACE VIEW vwEvaluationGroupData AS
SELECT
    egd.keyid,
    egd.evaluationid,
    egd.questiongroupid,
    (SELECT ed.questiongroupname
        FROM evaldetails ed
        WHERE ed.questiongroupid = egd.questiongroupid
        LIMIT 1) AS questiongroupname,
    egd.totalscore,
    egd.maxtotalscore,
    egd.markedna,
    egd.totalcriticalscore,
    egd.maxtotalcriticalscore,
    egd.totalnoncriticalscore,
    egd.maxtotalnoncriticalscore,
    egd.totalscoreunweighted,
    egd.maxtotalscoreunweighted,
    egd.failedkillquestions,
    ud.divisionid,
    egd.comments,
    eda.conversationid
FROM evalquestiongroupdata egd
LEFT JOIN evaldata eda ON eda.evaluationid = egd.evaluationid
LEFT JOIN userdetails ud ON ud.id = eda.userid;
