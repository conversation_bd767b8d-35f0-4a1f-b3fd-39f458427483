-- Drop the view if it exists (in case it was previously a materialized view)
DROP VIEW IF EXISTS mvwevaluationquestiondata;

-- Create regular view to replace any previous materialized view
CREATE OR REPLACE VIEW vwEvaluationQuestionData AS
SELECT
  DISTINCT eq.keyid,
  eq.evaluationid,
  eq.questionid,
  eq.answerid,
  eq.score,
  ed.questionanswervalue,
  eq.markedna,
  eq.failedkillquestions,
  eq.comments,
  ed.evaluationformid,
  ed.evaluationname,
  ed.questiongroupid,
  ed.questiongroupname,
  ed.questiontext,
  ed.questionhelptext,
  ed.quesiontype,
  ed.questionanwserid,
  ed.questionanswertext,
  cd.divisionid
FROM
  evalquestiondata eq
  LEFT JOIN evaldetails ed ON ed.evaluationformid :: text = eq.evaluationformid :: text
  AND ed.questiongroupid :: text = eq.questiongroupid :: text
  AND ed.questionid :: text = eq.questionid :: text
  AND ed.questionanwserid :: text = eq.answerid :: text
  LEFT JOIN evaldata eda ON eda.evaluationid :: text = eq.evaluationid :: text
  LEFT JOIN convsummarydata cd ON cd.conversationid :: text = eda.conversationid :: text;