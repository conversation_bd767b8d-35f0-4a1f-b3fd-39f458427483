-- Drop the table if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoicesentimentdetaildata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoicesentimentdetaildata;
IF OBJECT_ID('mvwconvvoicesentimentdetaildata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoicesentimentdetaildata;
GO

-- Create regular view to replace the table
CREATE OR ALTER VIEW vwConvVoiceSentimentDetailData AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.duration,
    ct.participant,
    ct.phrase,
    ct.sentiment,
    ct.phraseindex,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    qd1.name AS firstqueuename,
    cs.lastqueueid,
    qd2.name AS lastqueuename,
    cs.firstagentid,
    ud1.name AS firstagentname,
    ud1.department AS firstagentdept,
    ud1.managerid AS firstagentmanagerid,
    ud1.managername AS firstagentmanagername,
    cs.lastagentid,
    ud2.name AS lastagentname,
    ud2.department AS lastagentdept,
    ud2.managerid AS lastagentmanagerid,
    ud2.managername AS lastagentmanagername,
    cs.firstwrapupcode,
    wd1.name AS firstwrapupname,
    cs.lastwrapupcode,
    wd2.name AS lastwrapupname,
    dd.name AS divisionname
FROM convVoiceSentimentDetailData ct
LEFT JOIN convSummaryData cs ON cs.conversationid = ct.conversationid
LEFT JOIN divisionDetails dd ON cs.divisionid = dd.id
LEFT JOIN queueDetails qd1 ON qd1.id = cs.firstqueueid
LEFT JOIN queueDetails qd2 ON qd2.id = cs.lastqueueid
LEFT JOIN wrapupDetails wd1 ON wd1.id = cs.firstwrapupcode
LEFT JOIN wrapupDetails wd2 ON wd2.id = cs.lastwrapupcode
LEFT JOIN vwUserDetail ud1 ON ud1.id = cs.firstagentid
LEFT JOIN vwUserDetail ud2 ON ud2.id = cs.lastagentid;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'View for conversation voice sentiment detail data (converted from table)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'vwConvVoiceSentimentDetailData';
GO
