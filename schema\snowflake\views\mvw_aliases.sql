-- Backward compatibility aliases for converted mvw* views
-- These aliases ensure existing queries continue to work after the mvw* to vw* conversion

-- Alias for mvwevaluationoverview -> vwEvaluationOverview
CREATE OR REPLACE VIEW mvwevaluationoverview AS
SELECT * FROM vwEvaluationOverview;

-- Alias for mvwevaluationquestiondata -> vwEvaluationQuestionData  
CREATE OR REPLACE VIEW mvwevaluationquestiondata AS
SELECT * FROM vwEvaluationQuestionData;

-- Alias for mvwevaluationgroupdata -> vwEvaluationGroupData
CREATE OR REPLACE VIEW mvwevaluationgroupdata AS
SELECT * FROM vwEvaluationGroupData;

-- Comments for documentation
COMMENT ON VIEW mvwevaluationoverview IS 'Backward compatibility alias for vwEvaluationOverview - DEPRECATED: Use vwEvaluationOverview instead';
COMMENT ON VIEW mvwevaluationquestiondata IS 'Backward compatibility alias for vwEvaluationQuestionData - DEPRECATED: Use vwEvaluationQuestionData instead';
COMMENT ON VIEW mvwevaluationgroupdata IS 'Backward compatibility alias for vwEvaluationGroupData - DEPRECATED: Use vwEvaluationGroupData instead';
