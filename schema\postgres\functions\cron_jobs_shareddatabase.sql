-- Template for cron
/*
SELECT
    cron.schedule(
        'Description',
        'Cron schedule (see: crontab.guru',
        'Command to run',
        'Database to process against'
    );
*/

SET
    search_path TO cron;

-- Remove any existing MVW cron jobs that are no longer needed
-- These jobs are removed because MVW objects have been converted to real-time views
SELECT cron.unschedule('mvwconvvoiceoverviewdata_customername') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwconvvoiceoverviewdata_customername');
SELECT cron.unschedule('mvwconvvoicetopicdetaildata_customername') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwconvvoicetopicdetaildata_customername');
SELECT cron.unschedule('mvwconvvoicesentimentdetaildata_customername') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwconvvoicesentimentdetaildata_customername');
SELECT cron.unschedule('mvwevaluationoverview_customername') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwevaluationoverview_customername');
SELECT cron.unschedule('mvwevaluationgroupdata_customername') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwevaluationgroupdata_customername');
SELECT cron.unschedule('evaluationquestiondata_customername') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'evaluationquestiondata_customername');

-- MVW cron jobs removed - converted to real-time views
-- The following cron jobs are no longer needed as MVW objects have been converted to regular views:
-- - mvwconvvoiceoverviewdata_customername (now vwConvVoiceOverviewData)
-- - mvwconvvoicetopicdetaildata_customername (now vwConvVoiceTopicDetailData)
-- - mvwconvvoicesentimentdetaildata_customername (now vwConvVoiceSentimentDetailData)
-- - mvwevaluationoverview_customername (now vwEvaluationOverview)
-- - mvwevaluationgroupdata_customername (now vwEvaluationGroupData)
-- - evaluationquestiondata_customername (now vwEvaluationQuestionData)
SELECT
    cron.schedule(
        'archivequeueinteraction_customername',
        '0 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archivequeueinteraction(0,''D'')'
    );

SELECT
    cron.schedule(
        'archiveuserinteraction_customername',
        '15 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archiveuserinteraction(0,''D'')'
    );

SELECT
    cron.schedule(
        'archiveuserpresence_customername',
        '30 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archiveuserpresence(0,''D'');'
    );

SELECT
    cron.schedule(
        'archivebacklog_customername',
        '0 1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.archivebacklog()'
    );
